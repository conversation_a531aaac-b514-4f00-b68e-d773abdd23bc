#include "dynamic_list.h"

#include "log.h"
#include <stdio.h>
#include <stdlib.h>

#define DL_INITIAL_CAPACITY 2
#define DL_RESIZE_FACTOR 2

DynamicList *dl_create(void *(*clone)(const void *element),
                       void (*destruct)(void *element)) {
  DynamicList *list;
  LOG_CHECK_MALLOC(list, malloc(sizeof(DynamicList)));
  if (!list) {
    // Return NULL if the list structure itself can't be allocated
    return NULL;
  }

  list->capacity = DL_INITIAL_CAPACITY;
  list->length = 0;
  
  // Allocate memory for the elements array
  LOG_CHECK_MALLOC(list->elements,
                   (void **)malloc(sizeof(void *) * (size_t)list->capacity));

  // If the allocation for elements fails, free the list structure to prevent a memory leak
  if (!list->elements) {
    free(list);
    return NULL;
  }

  // Initialize the pointers to NULL
  for (size_t i = 0; i < list->capacity; i++) {
    list->elements[i] = NULL;
  }

  list->clone_element = clone;
  list->destruct_element = destruct;

  return list;
}

DynamicList *dl_clone(const DynamicList *list) {
  if (!list) {
    last_error_set("trying to clone a NULL list");
    return NULL;
  }

  // create a new list
  DynamicList *result = dl_create(list->clone_element, list->destruct_element);
  if (!result)
    return NULL;

  // copy elements
  for (size_t i = 0; i < list->length; i++) {
    if (dl_append(result, dl_get(list, i))) {
      // If append fails, clean up the partially created list
      dl_destruct(result);
      free(result);
      return NULL;
    }
  }

  return result;
}

static int reallocate_memory(DynamicList *list) {
  const int new_capacity = DL_RESIZE_FACTOR * list->capacity;
  void **new_address;
  LOG_CHECK_MALLOC(new_address, realloc(list->elements,
                                        sizeof(void *) * (size_t)new_capacity));

  list->elements = new_address;
  for (int i = list->length; i < new_capacity; i++)
    list->elements[i] = NULL;

  list->capacity = new_capacity;

  return 0;
}

int dl_append_by_value(DynamicList *list, void *element) {
  if (list->length == list->capacity)
    if (reallocate_memory(list)) {
      free(element);
      return 1;
    }

  // appending new element
  list->elements[list->length++] = element;

  return 0;
}

int dl_append(DynamicList *list, const void *element) {
  void *new_element = list->clone_element(element);
  if (!new_element)
    return 1;

  return dl_append_by_value(list, new_element);
}

int dl_set_by_value(DynamicList *list, void *element, size_t index) {
  if (index >= list->length) {
    last_error_set("trying to set element at index %zu in  a list of size %zu",
                   index, list->length);
    if (element)
      free(element);
    return 1;
  }

  list->destruct_element(list->elements[index]);
  list->elements[index] = element;

  return 0;
}

int dl_set(DynamicList *list, const void *element, size_t index) {
  void *new_el = list->clone_element(element);
  if (!new_el)
    return 1;

  return dl_set_by_value(list, new_el, index);
}

const void *dl_get(const DynamicList *list, size_t index) {
  if (index >= list->length) {
    last_error_set("can't access element at position %d in list of size %d",
                   index, list->length);
    return NULL;
  }

  return list->elements[index];
}

size_t dl_length(const DynamicList *list) { return list->length; }

void dl_destruct(DynamicList *list) {
  if (!list)
    return;

  for (size_t i = 0; i < list->length; i++) {
    if (list->elements[i]) {
      list->destruct_element(list->elements[i]);
      list->elements[i] = NULL;
    }
  }

  free(list->elements);

  list->capacity = 0;
  list->length = 0;
}
